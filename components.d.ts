/* eslint-disable */
/* prettier-ignore */
// @ts-nocheck
// Generated by unplugin-vue-components
// Read more: https://github.com/vuejs/core/pull/3399
export {}

declare module 'vue' {
  export interface GlobalComponents {
    AttachReader: typeof import('./src/packages/discordRenderer/components/attachReader.vue')['default']
    CustomTabOverview: typeof import('./src/views/chatting/mail/components/CustomTabOverview.vue')['default']
    DataOverview: typeof import('./src/views/chatting/mail/components/DataOverview.vue')['default']
    DiscordRenderer: typeof import('./src/packages/discordRenderer/index.vue')['default']
    ElAside: typeof import('element-plus/es')['ElAside']
    ElAvatar: typeof import('element-plus/es')['ElAvatar']
    ElBadge: typeof import('element-plus/es')['ElBadge']
    ElBreadcrumb: typeof import('element-plus/es')['ElBreadcrumb']
    ElBreadcrumbItem: typeof import('element-plus/es')['ElBreadcrumbItem']
    ElButton: typeof import('element-plus/es')['ElButton']
    ElButtonGroup: typeof import('element-plus/es')['ElButtonGroup']
    ElCard: typeof import('element-plus/es')['ElCard']
    ElCascader: typeof import('element-plus/es')['ElCascader']
    ElCascaderPanel: typeof import('element-plus/es')['ElCascaderPanel']
    ElCheckbox: typeof import('element-plus/es')['ElCheckbox']
    ElCheckboxGroup: typeof import('element-plus/es')['ElCheckboxGroup']
    ElCol: typeof import('element-plus/es')['ElCol']
    ElCollapse: typeof import('element-plus/es')['ElCollapse']
    ElCollapseItem: typeof import('element-plus/es')['ElCollapseItem']
    ElConfigProvider: typeof import('element-plus/es')['ElConfigProvider']
    ElContainer: typeof import('element-plus/es')['ElContainer']
    ElDatePicker: typeof import('element-plus/es')['ElDatePicker']
    ElDescriptions: typeof import('element-plus/es')['ElDescriptions']
    ElDescriptionsItem: typeof import('element-plus/es')['ElDescriptionsItem']
    ElDialog: typeof import('element-plus/es')['ElDialog']
    ElDivider: typeof import('element-plus/es')['ElDivider']
    ElDrawer: typeof import('element-plus/es')['ElDrawer']
    ElDropdown: typeof import('element-plus/es')['ElDropdown']
    ElDropdownItem: typeof import('element-plus/es')['ElDropdownItem']
    ElDropdownMenu: typeof import('element-plus/es')['ElDropdownMenu']
    ElEmpty: typeof import('element-plus/es')['ElEmpty']
    ElForm: typeof import('element-plus/es')['ElForm']
    ElFormItem: typeof import('element-plus/es')['ElFormItem']
    ElHeader: typeof import('element-plus/es')['ElHeader']
    ElIcon: typeof import('element-plus/es')['ElIcon']
    ElImage: typeof import('element-plus/es')['ElImage']
    ElImagePreview: typeof import('element-plus/es')['ElImagePreview']
    ElImageViewer: typeof import('element-plus/es')['ElImageViewer']
    ElInput: typeof import('element-plus/es')['ElInput']
    ElInputNumber: typeof import('element-plus/es')['ElInputNumber']
    ElLink: typeof import('element-plus/es')['ElLink']
    ElMain: typeof import('element-plus/es')['ElMain']
    ElMenu: typeof import('element-plus/es')['ElMenu']
    ElMenuItem: typeof import('element-plus/es')['ElMenuItem']
    ElOption: typeof import('element-plus/es')['ElOption']
    ElPagination: typeof import('element-plus/es')['ElPagination']
    ElPopconfirm: typeof import('element-plus/es')['ElPopconfirm']
    ElPopover: typeof import('element-plus/es')['ElPopover']
    ElProgress: typeof import('element-plus/es')['ElProgress']
    ElRadio: typeof import('element-plus/es')['ElRadio']
    ElRadioButton: typeof import('element-plus/es')['ElRadioButton']
    ElRadioGroup: typeof import('element-plus/es')['ElRadioGroup']
    ElRow: typeof import('element-plus/es')['ElRow']
    ElScrollbar: typeof import('element-plus/es')['ElScrollbar']
    ElSelect: typeof import('element-plus/es')['ElSelect']
    ElSelectV2: typeof import('element-plus/es')['ElSelectV2']
    ElSubMenu: typeof import('element-plus/es')['ElSubMenu']
    ElSwitch: typeof import('element-plus/es')['ElSwitch']
    ElTable: typeof import('element-plus/es')['ElTable']
    ElTableColumn: typeof import('element-plus/es')['ElTableColumn']
    ElTabPane: typeof import('element-plus/es')['ElTabPane']
    ElTabs: typeof import('element-plus/es')['ElTabs']
    ElTag: typeof import('element-plus/es')['ElTag']
    ElTimeline: typeof import('element-plus/es')['ElTimeline']
    ElTimelineItem: typeof import('element-plus/es')['ElTimelineItem']
    ElTimePicker: typeof import('element-plus/es')['ElTimePicker']
    ElTooltip: typeof import('element-plus/es')['ElTooltip']
    ElTree: typeof import('element-plus/es')['ElTree']
    ElUpload: typeof import('element-plus/es')['ElUpload']
    ElWatermark: typeof import('element-plus/es')['ElWatermark']
    EmailActions: typeof import('./src/views/chatting/mail/components/EmailActions.vue')['default']
    EmailCard: typeof import('./src/views/chatting/mail/components/EmailCard.vue')['default']
    EmailContent: typeof import('./src/views/chatting/mail/components/EmailContent.vue')['default']
    EmailDetail: typeof import('./src/views/chatting/mail/components/EmailDetail.vue')['default']
    EmailList: typeof import('./src/views/chatting/mail/components/EmailList.vue')['default']
    EmailListSort: typeof import('./src/views/chatting/mail/components/EmailListSort.vue')['default']
    EmailReply: typeof import('./src/views/chatting/mail/components/EmailReply.vue')['default']
    EmbedReader: typeof import('./src/packages/discordRenderer/components/embedReader.vue')['default']
    OpsChart: typeof import('./src/packages/opsChart/index.vue')['default']
    OpsEditer: typeof import('./src/packages/opsEditer/index.vue')['default']
    OpsInput: typeof import('./src/packages/opsInput/index.vue')['default']
    OpsSelect: typeof import('./src/packages/opsSelect/index.vue')['default']
    OpsTable: typeof import('./src/packages/opsTable/index.vue')['default']
    OpsVideo: typeof import('./src/packages/opsVideo/index.vue')['default']
    PollReader: typeof import('./src/packages/discordRenderer/components/pollReader.vue')['default']
    RouterLink: typeof import('vue-router')['RouterLink']
    RouterView: typeof import('vue-router')['RouterView']
    SearchForm: typeof import('./src/views/chatting/mail/components/SearchForm.vue')['default']
    SearchFormActions: typeof import('./src/views/chatting/mail/components/SearchFormActions.vue')['default']
    StickersReader: typeof import('./src/packages/discordRenderer/components/stickersReader.vue')['default']
  }
  export interface ComponentCustomProperties {
    vLoading: typeof import('element-plus/es')['ElLoadingDirective']
  }
}
